import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { Config } from '../../../config/config'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'

interface IOpenAIInitParams {
  model?: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  reasoning_effort?: string,
  timeout?: number
}

export const CLIENT_DEFAULT_TIMEOUT  = 2 * 60 * 1000 // 2 minutes

export class AzureOpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): AzureChatOpenAI {
    const {
      model = 'gpt-4.1 ',
      temperature = 0,
      maxTokens = 1024,
      topP = 0.9,
      frequencyPenalty = 0.5,
      reasoning_effort = 'minimal', // 'minimal'|'low'|'medium'|'high'（GPT-5 额外支持 'minimal'）
      timeout = CLIENT_DEFAULT_TIMEOUT,
    } = params

    if (timeout < 1000) {
      throw new Error('Timeout 以毫秒为单位，建议 ≥ 1000ms')
    }

    const isReasoningModel = [
      'gpt-5',
      'gpt-5-mini',
      'gpt-5-nano',
      'o4-mini',
    ].includes(model)

    const azureConfig: any = {
      // Azure 下这里要传“部署名”（通常与你的 model 同名）
      model,
      timeout,

      // Azure 认证与端点
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      azureOpenAIApiDeploymentName: model,
      configuration: { baseURL: Config.setting.azureOpenAI.apiBaseUrl },
      maxRetries: 1,
    }

    if (isReasoningModel) {
      azureConfig.maxCompletionTokens = Math.max(1, maxTokens + 256)
      azureConfig.reasoning = { 'effort': reasoning_effort }
    } else {
      azureConfig.temperature = temperature
      azureConfig.maxTokens = maxTokens
      azureConfig.topP = topP
      azureConfig.frequencyPenalty = frequencyPenalty
    }
    return new AzureChatOpenAI(azureConfig)
  }
}

export class OpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-4.1',
      temperature = 0,
      maxTokens = 1000,
      topP = 0.9,
      frequencyPenalty = 0.8,
      timeout = CLIENT_DEFAULT_TIMEOUT
    } = params

    if (timeout < 1000) {
      throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时')
    }

    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      maxTokens: maxTokens,
      topP: topP,
      frequencyPenalty: frequencyPenalty,
      timeout: timeout,
      apiKey: Config.setting.openai.apiKeys[0],
      configuration: { baseURL: Config.setting.openai.apiBaseUrl },
      maxRetries: 1
    })
  }
}

export class StableClaude {
  public static getClient(model = 'claude-3-5-sonnet-20241022', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.stableClaude.apiKey,
      configuration: {
        baseURL: Config.setting.stableClaude.apiBaseUrl
      },
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}

export class CheapOpenAI {
  public static getClient(model = 'gpt-4.1', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: Config.setting.cheapOpenAI.apiBaseUrl
      },
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}

export class MiTaAI {
  public static getClient(model : 'concise' | 'detail' | 'research', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
      modelName: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}

export class QwenMax {
  public static getClient(temperature = 0) {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      temperature: temperature,
      model: 'qwen-max',
    })
  }
}

export class PerplexityAI {
  public static getClient(temperature = 0) {
    return new ChatOpenAI({
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
      modelName: 'llama-3-sonar-large-32k-online',
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}