'use client'

import { useState } from 'react'
import { ITask } from '../../../../../bot/service/moer/components/visualized_sop/visualized_sop_type'
import Task from './task'
import { SopCalendarView } from './SopCalendarView'
import { FiGrid, FiCalendar } from 'react-icons/fi'

export function UserExistSop({ jobs, chatId } : {jobs: ITask[], chatId:string}) {
  const [viewMode, setViewMode] = useState<'calendar' | 'table'>('calendar')

  return (
    <div>
      {/* View Mode Toggle */}
      <div className="flex justify-between items-center mb-6 p-6 pb-0">
        <h1 className="text-3xl font-bold">用户 SOP 任务</h1>
        <div className="flex gap-2">
          <button
            className={`btn btn-sm ${viewMode === 'calendar' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setViewMode('calendar')}
          >
            <FiCalendar className="mr-1" />
            日历视图
          </button>
          <button
            className={`btn btn-sm ${viewMode === 'table' ? 'btn-primary' : 'btn-ghost'}`}
            onClick={() => setViewMode('table')}
          >
            <FiGrid className="mr-1" />
            表格视图
          </button>
        </div>
      </div>

      {/* Calendar View */}
      {viewMode === 'calendar' ? (
        <SopCalendarView tasks={jobs} chatId={chatId} />
      ) : (
        /* Table View */
        <div className="p-6">
          <table className="table">
            <thead>
              <tr>
                <th>name</th>
                <th>sendTime</th>
                <th>action</th>
              </tr>
            </thead>
            <tbody>
              {jobs.map((item, index) => <Task key={index} task={item}/>)}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}