'use client'

import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import Link from 'next/link'
import { ITask } from '../../../../../bot/service/moer/components/visualized_sop/visualized_sop_type'
import { FiChevronLeft, FiChevronRight, FiCalendar, FiClock, FiPlay } from 'react-icons/fi'

interface SopCalendarViewProps {
  tasks: ITask[]
  chatId: string
}

export function SopCalendarView({ tasks, chatId }: SopCalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState(dayjs())
  const [weekData, setWeekData] = useState<{ [key: string]: ITask[] }>({})

  useEffect(() => {
    const weekStart = getStartOfWeek(selectedDate)
    const weekEnd = getEndOfWeek(selectedDate)

    // Group tasks by date
    const groupedTasks: { [key: string]: ITask[] } = {}

    tasks.forEach((task) => {
      const taskDate = dayjs(task.sendTime)
      if (taskDate.isAfter(weekStart.subtract(1, 'day')) && taskDate.isBefore(weekEnd.add(1, 'day'))) {
        const dateKey = taskDate.format('YYYY-MM-DD')
        if (!groupedTasks[dateKey]) {
          groupedTasks[dateKey] = []
        }
        groupedTasks[dateKey].push(task)
      }
    })

    setWeekData(groupedTasks)
  }, [selectedDate, tasks])

  const getStartOfWeek = (date: dayjs.Dayjs) => {
    return date.startOf('week').add(1, 'day') // Start from Monday
  }

  const getEndOfWeek = (date: dayjs.Dayjs) => {
    return date.endOf('week').add(1, 'day') // End on Sunday
  }

  const goToPreviousWeek = () => {
    setSelectedDate((prev) => prev.subtract(1, 'week'))
  }

  const goToNextWeek = () => {
    setSelectedDate((prev) => prev.add(1, 'week'))
  }

  const goToToday = () => {
    setSelectedDate(dayjs())
  }

  const getWeekDays = () => {
    const startOfWeek = getStartOfWeek(selectedDate)
    const days = []
    for (let i = 0; i < 7; i++) {
      days.push(startOfWeek.add(i, 'day'))
    }
    return days
  }

  const weekDays = getWeekDays()
  const weekStart = getStartOfWeek(selectedDate)
  const weekEnd = getEndOfWeek(selectedDate)

  return (
    <div className="p-6 bg-base-100 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        <div className="flex items-center gap-4">
          <FiCalendar className="text-2xl text-primary" />
          <h1 className="text-3xl font-bold text-base-content">SOP 任务日历</h1>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={goToPreviousWeek}
            className="btn btn-ghost btn-sm"
          >
            <FiChevronLeft className="text-lg" />
          </button>

          <button
            onClick={goToToday}
            className="btn btn-primary btn-sm"
          >
            今天
          </button>

          <button
            onClick={goToNextWeek}
            className="btn btn-ghost btn-sm"
          >
            <FiChevronRight className="text-lg" />
          </button>
        </div>
      </div>

      {/* Week Range Display */}
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-base-content">
          {weekStart.format('YYYY年MM月DD日')} - {weekEnd.format('MM月DD日')}
        </h2>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 calendar-fade-in">
        {weekDays.map((day, index) => {
          const dateKey = day.format('YYYY-MM-DD')
          const dayTasks = weekData[dateKey] || []
          const isToday = day.isSame(dayjs(), 'day')
          const isWeekend = day.day() === 0 || day.day() === 6

          return (
            <div
              key={dateKey}
              className={`
                card bg-base-200 shadow-sm border-2 min-h-[300px]
                ${isToday ? 'border-primary bg-primary/5' : 'border-base-300'}
                ${isWeekend ? 'bg-base-300/50' : ''}
              `}
            >
              <div className="card-body p-4">
                {/* Day Header */}
                <div className="flex justify-between items-center mb-3">
                  <div className="text-center">
                    <div className="text-sm font-medium text-base-content/70">
                      {day.format('ddd')}
                    </div>
                    <div className={`
                      text-lg font-bold
                      ${isToday ? 'text-primary' : 'text-base-content'}
                    `}>
                      {day.format('DD')}
                    </div>
                  </div>
                  <div className="badge badge-ghost badge-sm">
                    {dayTasks.length}
                  </div>
                </div>

                {/* Tasks */}
                <div className="space-y-2 flex-1">
                  {dayTasks.map((task, taskIndex) => (
                    <div
                      key={`${task.name}-${taskIndex}`}
                      className="card card-compact bg-base-100 shadow-sm task-card"
                    >
                      <div className="card-body p-3">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <div className="relative group">
                              <h4 className="font-medium text-sm text-base-content line-clamp-2 leading-tight">
                                {task.name}
                              </h4>
                              <div className="absolute z-99 -left-5 top-full mt-1 w-max max-w-xs bg-base-200 text-base-content text-xs rounded shadow-lg px-3 py-2 whitespace-pre-line break-all opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-200">
                                {task.name}
                              </div>
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <FiClock className="text-xs text-base-content/60 flex-shrink-0" />
                              <span className="text-xs text-base-content/60">
                                {dayjs(task.sendTime).format('HH:mm')}
                              </span>
                            </div>
                          </div>
                          <div className="flex flex-col gap-1 flex-shrink-0">
                            {task.sopId && (
                              <Link
                                href={`../../sop/${task.sopId}`}
                                className="btn btn-xs btn-primary btn-outline hover:btn-primary"
                              >
                                <FiPlay className="text-xs" />
                                详情
                              </Link>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {dayTasks.length === 0 && (
                    <div className="flex items-center justify-center h-20 text-base-content/40">
                      <span className="text-sm">无任务</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Summary */}
      <div className="mt-8 stats shadow w-full">
        <div className="stat">
          <div className="stat-title">本周任务总数</div>
          <div className="stat-value text-primary">
            {Object.values(weekData).flat().length}
          </div>
        </div>
        <div className="stat">
          <div className="stat-title">今日任务</div>
          <div className="stat-value text-info">
            {weekData[dayjs().format('YYYY-MM-DD')]?.length || 0}
          </div>
        </div>
        <div className="stat">
          <div className="stat-title">总任务数</div>
          <div className="stat-value text-success">
            {tasks.length}
          </div>
        </div>
      </div>
    </div>
  )
}
