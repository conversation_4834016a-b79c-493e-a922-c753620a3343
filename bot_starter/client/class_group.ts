import { Job, Queue, Worker } from 'bullmq'
import { RedisDB } from '../../bot/model/redis/redis'
import { Config } from '../../bot/config/config'
import { ClassGroupSend } from '../../bot/service/moer/components/flow/schedule/task/classGroupSend'
import { ITask } from '../../bot/service/moer/components/schedule/type'
import { calTaskTime, IScheduleTime } from '../../bot/service/moer/components/schedule/creat_schedule_task'
import { ScheduleTask } from '../../bot/service/moer/components/schedule/schedule'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { DataService } from '../../bot/service/moer/getter/getData'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { ChatStateStore } from '../../bot/service/moer/storage/chat_state_store'
import logger from '../../bot/model/logger/logger'
import { HashSum } from '../../bot/lib/hash/hash'
import RateLimiter from '../../bot/model/redis/rate_limiter'

export class ClassGroupTaskManager {
  /**
   * 获取队列名称
   */
  public static getQueueName(groupId?: string): string {
    const accountId = Config.setting.wechatConfig?.id
    if (groupId) {
      return `${accountId}_${groupId.replaceAll(':', '')}`
    }
    // 向后兼容：如果没有传入 groupId，使用第一个群ID
    const classGroupId = Config.setting.wechatConfig?.classGroupId
    const firstGroupId = Array.isArray(classGroupId) ? classGroupId[0] : classGroupId
    return `${accountId}_${firstGroupId?.replaceAll(':', '') || ''}`
  }

  /**
   * 获取所有群ID列表
   */
  public static getGroupIds(): string[] {
    const classGroupId = Config.setting.wechatConfig?.classGroupId
    return Array.isArray(classGroupId) ? classGroupId : [classGroupId || '']
  }

  /**
   * 创建群任务
   */
  public static async createTasks(): Promise<void> {
    const groupIds = this.getGroupIds()

    for (const groupId of groupIds) {
      await this.createTasksForGroup(groupId)
    }
  }

  /**
   * 为指定群创建任务
   */
  private static async createTasksForGroup(groupId: string): Promise<void> {
    const queueName = this.getQueueName(groupId)
    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    // 创建对应的 chat 信息
    if (!await ChatDB.getById(queueName)) {
      await PrismaMongoClient.getInstance().chat.create({
        data: {
          id: queueName,
          contact: {
            wx_id: queueName,
            wx_name: '班级群任务',
          },
          chat_state: ChatStateStore.get(queueName),
          course_no: DataService.getNextWeekCourseNo(),
          wx_id: Config.setting.wechatConfig?.id as string,
        }
      })
    } else {
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: queueName
        },
        data: {
          course_no: DataService.getNextWeekCourseNo(),
        }
      })
    }

    // 清空队列，以防重复添加任务
    await queue.obliterate({ force: true })

    // 获取任务
    const tasks = await ClassGroupSend.getTask(groupId)
    for (const task of tasks) {
      task.sendTime = await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId, true)
    }

    // 添加任务到队列
    await ScheduleTask.addTasks(queueName, tasks)
  }

  /**
   * 启动任务监听
   */
  public static async startWorker() {
    const groupIds = this.getGroupIds()

    for (const groupId of groupIds) {
      await this.startWorkerForGroup(groupId)
    }

    // 启动创建任务的定时器（只需要一个）
    await this.startTaskCreationScheduler()
  }

  /**
   * 为指定群启动 Worker
   */
  private static async startWorkerForGroup(groupId: string): Promise<void> {
    const queueName = this.getQueueName(groupId)
    logger.trace('启动群任务监听:', queueName, 'groupId:', groupId)

    new Worker(queueName, async (job: Job) => {
      if (Config.isTestAccount()) return  // 测试账号不进行群发
      if (!Config.setting.localTest && !Config.isGroupOwner()) {
        logger.trace('非群主不进行群发')
        return
      }
      try {
        const limiter = new RateLimiter({
          windowSize: 60 * 60,
          maxRequests: 1
        })

        const isAllowed = await limiter.isAllowed(HashSum.hash(JSON.stringify(job.data)), 'class_group_task')

        if (!isAllowed) {
          return false
        }

        await new ClassGroupSend().process(job.data as ITask)
      } catch (e) {
        logger.error('群任务执行失败', e)
      }
    }, {
      connection: RedisDB.getInstance(),
      lockDuration: 1800000 // 锁的有效期，单位为毫秒 (30 * 60 * 1000 = 30分钟)
    }).on('error', (err) => {
      logger.error('群 SOP Worker 发生未捕获错误', err)
    })
  }

  /**
   * 启动创建任务的定时器
   */
  private static async startTaskCreationScheduler(): Promise<void> {
    // 每周定时启动新的任务队列
    const createQueueName = `create_${Config.setting.wechatConfig?.id}_class_group_tasks`
    const queue = new Queue(createQueueName, { connection: RedisDB.getInstance() })
    await queue.obliterate({ force: true })

    // 添加重复任务
    await queue.add(
      'weeklyStartGroupTask',
      { timestamp: Date.now() },
      {
        repeat: { pattern: '0 30 20 * * 5', key: 'weeklyStartGroupTask' },
        jobId: 'weeklyStartGroupTask', // 固定的 jobId 确保只有一个任务
      }
    )

    // 启动每周 自动创建群任务监听
    new Worker(createQueueName, async (job: Job) => {
      // 周六重新创建任务
      logger.trace('周六创建群发任务队列')
      await ClassGroupTaskManager.createTasks()
    }, {
      connection: RedisDB.getInstance()
    }).on('error', (err) => {
      logger.error('创建群 SOP Worker 发生未捕获错误', err)
    })
  }
}